<?php

namespace App\Http\Controllers\MBI\API\Statistic;

use Exception;
use Carbon\Carbon;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\User;

class StatisticsController extends Controller
{
    public function statistics(Request $request)
    {
        try {
            // Get the date range from the request     
            $startDate = $request->startDate ? Carbon::parse($request->startDate)->startOfDay() : Carbon::now()->startOfDay();
            $endDate = $request->endDate ? Carbon::parse($request->endDate)->endOfDay() : Carbon::now()->endOfDay();
            
            if($request->has('tab') && $request->tab == 'all'){
                $start_date = Carbon::createFromDate(2024, 1, 1)->startOfYear()->format('Y-m-d');
                $end_date = Carbon::now()->endOfDay()->format('Y-m-d');
            } else if ($request->has('tab') && $request->tab == date('Y') - 1){
                $start_date = Carbon::createFromDate(date('Y') - 1, 1, 1)->startOfYear()->format('Y-m-d');
                $end_date = Carbon::createFromDate(date('Y') -1, 1, 1)->endOfYear()->format('Y-m-d');
            } else if ($request->has('tab') && $request->tab == date('Y')){
                $start_date = Carbon::createFromDate(date('Y'), 1, 1)->startOfYear()->format('Y-m-d');
                $end_date = Carbon::createFromDate(date('Y'), 1, 1)->endOfYear()->format('Y-m-d');
            } else if ($request->has('tab') && $request->tab == 'today'){
                $start_date = Carbon::now()->startOfDay()->format('Y-m-d');
                $end_date = Carbon::now()->endOfDay()->format('Y-m-d');
            }
           
            $userIds = User::where('access_module', 'like', '%plats%')
                        ->where('users.username', 'not like', 'uplats012')
                        ->pluck('id');

            $baseQuery = Order::whereNull('orders.deleted_at')
            ->join('users', 'users.id', '=', 'orders.user_id')
            ->join('companies', 'companies.id', '=', 'orders.company_id')
            ->leftJoin('pbts', 'pbts.id', '=', 'companies.pbt_id')
            ->leftJoin('user_details','users.id','=','user_details.user_id')
            ->where('users.access_module', 'like', '%plats%')
            ->where('users.username', 'not like', 'uplats012');

            if ($startDate && $endDate) {
                $baseQuery->whereBetween('orders.order_date', [
                    $startDate,
                    $endDate
                ]);
            }
            $reports = $baseQuery->selectRaw('SUM(orders.grandtotal_decimal) as sales_today')
            ->selectRaw('COUNT(orders.id) as total_transaction_today');

            
            $query = $baseQuery->first();

            // second query for list of users who made transaction during startDate and endDate. To be optimized later
            $data = $baseQuery->select(
                'users.id',
                'user_details.first_name',
                'companies.com_name',
                'pbts.code',
                )
            ->selectRaw('SUM(orders.grandtotal_decimal) as total_sales')
            ->selectRaw('COUNT(orders.id) as total_trx')
            ->groupBy('users.id',
             'user_details.first_name',
             'companies.com_name',
             'pbts.code'
             )
            ->orderBy($request->sortBy ?: 'total_sales', $request->sortDir ?: 'desc')
            ->paginate(10);

            // sort data based on user th click
            
            // if($request->sortDir === 'ASC'){
            //     $data = $data->sortBy($request->sortBy);
            // } else {
            //     $data = $data->sortByDesc($request->sortBy);
            // }


            // payment gateway
            $allPaymentMethod = Order::selectRaw(
                'pay_method, SUM(payment_received_decimal) as value, COUNT(*) as quantity'
            )
                ->whereNull('deleted_at')
                ->whereIn('user_id', $userIds)
                ->whereBetween('order_date', [$startDate, $endDate])
                ->groupBy('pay_method')
                ->havingRaw('value > 0 OR quantity > 1')
                ->orderBy('value', 'DESC')
                ->get();

                $payment_method = [];
        foreach($allPaymentMethod as $key => $item){
            $name = ($item->pay_method === 'QR_PAY') ? 'QR_GKASH' : $item->pay_method;
            $number = ($key == 0) ? '' : $key+1;
            $backgroundColors = [
                "!bg-primary-info" => "#ef4444",
                "!bg-primary-info2" => "#f97316",
                "!bg-primary-info3" => "#fcd34d",
                "!bg-primary-info4" => "#84cc16",
                "!bg-primary-info5" => "#4ade80",
                "!bg-primary-info6" => "#22d3ee",
                "!bg-primary-info7" => "#3b82f6",
                "!bg-primary-info8" => "#818cf8",
                "!bg-primary-info9" => "#e879f9",
                "!bg-primary-info10" => "#fb7185",
            ];
        
            $background = isset($backgroundColors["!bg-primary-info" . $number]) ? $backgroundColors["!bg-primary-info" . $number] : "!bg-primary-info";
        
            $payment_method[] = [
                'name' => $name,
                'quantity' => $item->quantity,
                'amount' => $item->value,
                'background' => $background,
            ];
        }

                return response()->json([
                    'status' => '1',
                    'sales' => "RM " . number_format($query->sales_today, 2),
                    'total_transaction' => number_format($query->total_transaction_today, 0, '', ','),
                    'payment_method' => $payment_method,
                    'users' => $data
                ]);
        } catch (Exception $e) {
            // Log the error message and stack trace
            Log::error('MBI API Error in statistics method: ' . $e->getMessage());
    
            return response()->json([
                'status' => '0',
                'error' => 'An error occurred while processing your request.',
                'message' => $e->getMessage()
            ], 500);
        }
       
    }
}
